Rails.application.routes.draw do
  # --- CONCERN PRO SPRÁVU BLOKŮ ---
  # S<PERSON>č<PERSON> sada rout pro všechny typy bloků (DRY princip).
  # Concern bude obsahovat POUZE sd<PERSON><PERSON> a<PERSON>, ne celou `resources` definici.
  concern :block_actions do
    member do
      patch :hide
      patch :sort
    end
    # Zde můžete přidat dalš<PERSON> sdílen<PERSON> akce, např. na collection.
  end

  # --- VEŘEJNÉ ROUTY A SYSTÉMOVÉ VĚCI ---
  resource :session
  resources :passwords, param: :token
  get "up" => "rails/health#show", as: :rails_health_check

  direct :cdn_image do |model, options|
    expires_in = options.delete(:expires_in) { ActiveStorage.urls_expire_in }

    if model.respond_to?(:signed_id)
      if Rails.env.production?
        route_for(:rails_service_blob_proxy, model.signed_id(expires_in: expires_in), model.filename, options.merge(host: "winweb.b-cdn.net"))
      else
        route_for(:rails_service_blob, model.signed_id, model.filename, options)
      end
    else
      signed_blob_id = model.blob.signed_id(expires_in: expires_in)
      variation_key  = model.variation.key
      filename       = model.blob.filename

      if Rails.env.production?
        route_for(:rails_blob_representation_proxy, signed_blob_id, variation_key, filename, options.merge(host: "winweb.b-cdn.net"))
      else
        route_for(:rails_blob_representation, signed_blob_id, variation_key, filename, options)
      end
    end
  end

  if Rails.env.development?
    mount Lookbook::Engine, at: "/lookbook"
  end

  if Rails.env.production?
    constraints subdomain: false do
      get ":any", to: redirect(subdomain: "www", path: "/%{any}"), any: /.*/
      root to: redirect(subdomain: "www", path: "/"), as: :non_www_root
    end
  end

  # --- ADMINISTRACE ---
  namespace :admin do
    namespace :content do
      get "layout_blocks/new"
      get "layout_blocks/create"
      get "layout_blocks/destroy"
      get "layout_blocks/sort"
      get "layouts/index"
      get "layouts/new"
      get "layouts/create"
      get "layouts/edit"
      get "layouts/update"
      get "layouts/destroy"
    end
    # Scope pro website_id je zde správně, aplikuje se na vše uvnitř.
    scope '/:website_id' do
      resource :dashboard, only: :show
      resources :sessions
      resources :reservations do
        member do
          patch :confirm
          patch :cancel
        end
      end
      resources :pricing do
        patch :sort, on: :member
        resources :pricing_sections, module: :pricing
      end

      resources :services do
        post ":id", on: :collection, to: "services#create"
      end

      resource :settings do
        resource :opening_hours, module: :settings
        resources :forms, module: :settings
        resources :languages, module: :settings
        resource :domain, module: :settings
        resource :social_networks, module: :settings
        resource :theme, module: :settings, only: [:show, :update] do
          patch :load_preset
        end
      end

      namespace :content do
        resources :pages, except: [:show] do
          member do
            patch :sort
            patch :toggle_visibility
          end
          get :search, on: :collection

          # Definujeme resources a použijeme concern pro vnořené akce.
          resources :blocks, controller: 'page_blocks' do
            concerns :block_actions
          end
        end

        resources :media_types

        # --- LAYOUTY A JEJICH BLOKY ---
        resources :layouts, except: [:show] do
          # Definujeme resources a použijeme concern pro vnořené akce.
          resources :blocks, controller: 'layout_blocks' do
            concerns :block_actions
          end
        end

        # --- Ostatní zdroje patřící pod 'content' ---
        resources :pricing_options, only: [:update]
        resources :media do
          patch :sort, on: :member
        end
      end
    end
  end

  # --- OSTATNÍ VEŘEJNÉ ROUTY ---
  resources :webhooks, only: [] do
    post ":event", on: :collection, to: "webhooks#create"
  end

  resources :reservations do
    get :times, on: :collection
    get :dates, on: :collection
  end

  resources :inbox_messages

  get "(:locale)", to: "pages#homepage", as: :homepage, constraints: { locale: %w[cs en sk pl] }
  get "(:locale)/:slug", to: "pages#show", as: :page, constraints: { locale: %w[cs en sk pl], slug: %r{[^/]+} }

  root "pages#homepage"
end
