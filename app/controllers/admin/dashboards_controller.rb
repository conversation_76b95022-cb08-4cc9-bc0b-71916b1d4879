class Admin::DashboardsController < Admin::ApplicationController
  def show
    add_breadcrumb "Dashboard"

    # <PERSON>ákladn<PERSON> statistiky
    @stats = {
      pages_count: @website.pages.count,
      published_pages_count: @website.pages.where.not(published_at: nil).count,
      reservations_count: @website.reservations.count,
      pending_reservations_count: @website.reservations.pending.count,
      media_count: @website.media.count,
      forms_count: @website.forms.count
    }

    # Nedávné aktivity
    @recent_pages = @website.pages.order(created_at: :desc).limit(5)
    @recent_reservations = @website.reservations.order(created_at: :desc).limit(5)
    @recent_media = @website.media.order(created_at: :desc).limit(5)

    # Nadcházejí<PERSON><PERSON> rezervace
    @upcoming_reservations = @website.reservations.pending
                                     .where('date >= ?', Date.current)
                                     .order(:date, :time)
                                     .limit(5)
  end
end
