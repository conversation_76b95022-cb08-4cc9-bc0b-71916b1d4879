# frozen_string_literal: true

module Admin
  class Content::PageBlocksController < Content::ContentController
    include BlockManagement
    include PageContextBuilder

    layout "editor"

    before_action :set_page
    before_action :set_block, only: [:edit, :update, :hide, :destroy, :sort]

    def index
      @blocks = PageCompositionService.new(@page).call(scope: :content_only)

      build_view_context(type: :admin)

      factory = BlockFactory.new(context: @view_context)

      @block_components = @blocks.each_with_object({}) do |block_model, hash|
        hash[block_model.id] = factory.build(block_model)
      end
    end

    def edit
      @controls = @block.controls_for_locale(@page.locale)
    end

    def new
      @block = Block.new

      if params[:add].present?
        block_type = params[:add].to_sym

        build_view_context(type: :admin)

        factory = BlockFactory.new(context: @view_context)

        @block_components = factory.build_by_type(block_type)
      end
    end

    def create
      def create
        build_view_context(type: :admin)

        factory = BlockFactory.new(context: @view_context)

        block_presenter = factory.create_presenter(params.dig(:block_type))

        new_block = Block.initialize_from_block_object(block_presenter)

        @page.blocks << new_block

        if new_block
          page_block = PageBlock.where(block: new_block, page: @page).take
          if params[:after].present?
            after_page_block = @page.page_blocks.find_by(block_id: params[:after])
            page_block.update(position: { after: after_page_block }) if after_page_block
          elsif params[:before].present?
            before_page_block = @page.page_blocks.find_by(block_id: params[:before])
            page_block.update(position: { before: before_page_block }) if before_page_block
          end
        end

        redirect_to admin_content_page_blocks_path(@page)
      end
    end

    def sort
      page_block = @page.page_blocks.find_by(block: @block)
      unless page_block
        redirect_to after_update_path, alert: "Vazba mezi stránkou a blokem nenalezena."
        return
      end

      case params[:direction]
      when "up"
        prev_item = page_block.previous_item
        page_block.update(position: { before: prev_item }) if prev_item
      when "down"
        next_item = page_block.next_item
        page_block.update(position: { after: next_item }) if next_item
      end
      redirect_to after_update_path, notice: "Pořadí bloků bylo změněno."
    end

    private

    def set_page
      @page = Page.friendly.find(params[:page_id])
      @owner ||= @page
    end

    def set_block
      @block = @page.blocks.find(params[:id])
    end

    def after_update_path
      admin_content_page_blocks_path(@page)
    end
  end
end
