# frozen_string_literal: true

module Admin
  class Content::LayoutBlocksController < Content::ContentController
    include BlockManagement
    include PageContextBuilder

    layout "editor"

    before_action :set_layout
    before_action :set_block, only: [:edit, :update, :hide, :destroy, :sort]

    def index
      @owner = @layout

      @layout_models = LayoutCompositionService.new(@layout).call

      build_view_context(type: :admin)

      factory = BlockFactory.new(context: @view_context)

      @layout_components = {
        main_header: factory.build(@layout_models[:main_header]),
        global_headers: @layout_models[:global_headers].map { |m| factory.build(m) },
        global_footers: @layout_models[:global_footers].map { |m| factory.build(m) },
        main_footer: factory.build(@layout_models[:main_footer])
      }
    end

    def edit
      @owner = @layout
      @controls = @block.controls_for_locale(@layout_locale.to_sym)
      @locale = @layout_locale

      render "admin/content/page_blocks/edit"
    end

    def new
      @owner = @layout
      @block = Block.new

      if params[:add].present?
        block_type = params[:add].to_sym

        build_view_context(type: :admin)

        factory = BlockFactory.new(context: @view_context)

        @block_components = factory.build_by_type(block_type)
      end

      render "admin/content/page_blocks/new"
    end

    def create
      build_view_context(type: :admin)

      factory = BlockFactory.new(context: @view_context)

      block_presenter = factory.create_presenter(params.dig(:block_type))

      new_block = Block.initialize_from_block_object(block_presenter)

      @layout.layout_blocks.create(block: new_block, location: params[:location])

      if params[:location] == "main_header"
        @layout.update(main_header_block: new_block)
      end

      if params[:location] == "main_footer"
        @layout.update(main_footer_block: new_block)
      end

      redirect_to admin_content_layout_blocks_path(@layout)
    end

    def sort
      layout_block = @layout.layout_blocks.find_by(block: @block)
      redirect_to after_update_path
    end

    private

    def set_layout
      @layout = Layout.find(params[:layout_id])
    end

    def set_block
      @block = Block.find(params[:id])
    end

    def owner
      @owner = @layout
    end

    def after_update_path
      admin_content_layout_blocks_path(@layout)
    end

    def set_layout_locale
      @layout_locale ||= available_locales.include?(params[:locale]) ? params[:locale] : Current.website.locale
    end
  end
end
